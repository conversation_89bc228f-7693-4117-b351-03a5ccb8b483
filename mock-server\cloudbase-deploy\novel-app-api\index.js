const fs = require('fs');
const path = require('path');
const tcb = require('@cloudbase/node-sdk');

// 初始化CloudBase SDK
const app = tcb.init({
  env: process.env.TCB_ENV || 'novel-app-2gywkgnn15cbd6a8'
});

const db = app.database();

// 读取数据库文件
function loadDatabase() {
  try {
    const dbPath = path.join(__dirname, 'data', 'db.json');
    if (fs.existsSync(dbPath)) {
      const dbContent = fs.readFileSync(dbPath, 'utf8');
      return JSON.parse(dbContent);
    }
  } catch (error) {
    console.error('Failed to load database:', error);
  }

  // 如果无法读取数据库，返回默认数据
  return {
    users: [
      {
        id: 'test-user-id',
        username: 'testuser',
        passwordHash: 'ecd71870d1963316a97e3ac3408c9835ad8cf0f3c1bc703527c30265534f75ae',
        phoneNumber: '13800138000',
        email: null,
        avatar: null,
        isMember: false,
        memberExpireTime: null,
        membershipType: 'none',
        isPermanentMember: false,
        isDataSyncEnabled: true,
        settings: {
          enableBiometric: false,
          autoSync: true,
          enableNotification: true,
          theme: 'system',
          language: 'zh-CN'
        }
      }
    ]
  };
}

// 标准化会员类型，确保符合Flutter应用的枚举值
function _normalizeMembershipType(membershipType) {
  if (!membershipType) return 'none';

  switch (membershipType.toLowerCase()) {
    case 'free':
    case 'none':
      return 'none';
    case 'monthly':
    case 'month':
      return 'monthly';
    case 'permanent':
    case 'lifetime':
    case 'forever':
      return 'permanent';
    default:
      return 'none';
  }
}

// 发送短信验证码（需要集成真实的短信服务）
async function sendSMS(phoneNumber, verificationCode) {
  console.log(`[模拟短信] 发送验证码到 ${phoneNumber}: ${verificationCode}`);

  // 方案1: 腾讯云短信服务集成（推荐）
  // const tencentSMS = require('tencentcloud-sdk-nodejs');
  // const SmsClient = tencentSMS.sms.v20210111.Client;
  //
  // const clientConfig = {
  //   credential: {
  //     secretId: process.env.TENCENT_SECRET_ID,
  //     secretKey: process.env.TENCENT_SECRET_KEY,
  //   },
  //   region: "ap-beijing",
  //   profile: {
  //     httpProfile: {
  //       endpoint: "sms.tencentcloudapi.com",
  //     },
  //   },
  // };
  //
  // const client = new SmsClient(clientConfig);
  // const params = {
  //   PhoneNumberSet: [phoneNumber],
  //   SmsSdkAppId: "您的短信应用ID",
  //   SignName: "您的短信签名",
  //   TemplateId: "您的模板ID",
  //   TemplateParamSet: [verificationCode],
  // };
  //
  // try {
  //   const result = await client.SendSms(params);
  //   return { success: true, result };
  // } catch (error) {
  //   console.error('腾讯云短信发送失败:', error);
  //   return { success: false, error };
  // }

  // 方案2: 阿里云短信服务
  // const Core = require('@alicloud/pop-core');
  // const client = new Core({
  //   accessKeyId: process.env.ALIBABA_ACCESS_KEY_ID,
  //   accessKeySecret: process.env.ALIBABA_ACCESS_KEY_SECRET,
  //   endpoint: 'https://dysmsapi.aliyuncs.com',
  //   apiVersion: '2017-05-25'
  // });
  //
  // const params = {
  //   PhoneNumbers: phoneNumber,
  //   SignName: '您的签名',
  //   TemplateCode: '您的模板代码',
  //   TemplateParam: JSON.stringify({ code: verificationCode })
  // };
  //
  // try {
  //   const result = await client.request('SendSms', params, { method: 'POST' });
  //   return { success: result.Code === 'OK', result };
  // } catch (error) {
  //   console.error('阿里云短信发送失败:', error);
  //   return { success: false, error };
  // }

  // 当前是模拟实现，开发阶段使用
  // 生产环境请替换为上述真实的短信服务集成
  return new Promise((resolve) => {
    setTimeout(() => {
      // 模拟90%的成功率
      const success = Math.random() > 0.1;
      resolve({
        success,
        message: success ? '模拟发送成功' : '模拟发送失败',
        debugInfo: `验证码: ${verificationCode}`
      });
    }, 1000); // 模拟网络延迟
  });
}

// CloudBase API入口
exports.main = async (event, context) => {
  console.log('=== CloudBase Function Called ===');
  console.log('Event:', JSON.stringify(event, null, 2));
  console.log('Context:', JSON.stringify(context, null, 2));

  try {
    // 处理OPTIONS请求
    if (event.httpMethod === 'OPTIONS') {
      return {
        statusCode: 200,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type,Authorization'
        },
        body: ''
      };
    }

    // 简单的路由处理
    const path = event.path || '/';
    const method = event.httpMethod || 'GET';

    console.log(`Processing ${method} ${path}`);

    // 基本的API响应
    if (path === '/api' || path === '/') {
      return {
        statusCode: 200,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*'
        },
        body: JSON.stringify({
          success: true,
          message: 'Novel App API is running on CloudBase',
          timestamp: new Date().toISOString(),
          path: path,
          method: method
        })
      };
    }

    // 登录接口
    if ((path === '/api/auth/login' || path === '/auth/login') && method === 'POST') {
      let body;
      try {
        body = event.body ? (typeof event.body === 'string' ? JSON.parse(event.body) : event.body) : {};
      } catch (e) {
        console.log('Failed to parse request body:', e);
        console.log('Raw body:', event.body);
        return {
          statusCode: 400,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
          body: JSON.stringify({
            success: false,
            message: '请求数据格式错误'
          })
        };
      }

      console.log('Login request body:', body);
      console.log('Username:', body.username);
      console.log('Password hash:', body.password);
      console.log('Request path:', path);
      console.log('Request method:', method);

      // 从数据库加载用户信息
      const database = loadDatabase();
      console.log('Database loaded, users count:', database.users.length);

      // 查找用户
      const user = database.users.find(u => u.username === body.username);
      console.log('Found user:', user ? user.username : 'not found');

      if (!user) {
        console.log('User not found:', body.username);
        return {
          statusCode: 400,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
          body: JSON.stringify({
            success: false,
            message: '用户名或密码错误'
          })
        };
      }

      // 验证密码
      console.log('Stored password hash:', user.passwordHash);
      console.log('Received password hash:', body.password);

      // 如果数据库中没有密码哈希，使用默认的test123哈希
      const expectedHash = user.passwordHash || 'ecd71870d1963316a97e3ac3408c9835ad8cf0f3c1bc703527c30265534f75ae';

      const userInfo = { passwordHash: expectedHash, user: user };
      if (userInfo && body.password === userInfo.passwordHash) {
        console.log('Login successful for user:', body.username);
        return {
          statusCode: 200,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
          body: JSON.stringify({
            success: true,
            data: {
              token: `token-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
              refreshToken: `refresh-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
              user: {
                id: user.id || '',
                username: user.username || '',
                phoneNumber: user.phoneNumber || '',
                email: user.email || null,
                avatar: user.avatar || null,
                isMember: user.isMember || false,
                memberExpireTime: user.memberExpireTime || null,
                membershipType: _normalizeMembershipType(user.membershipType),
                isPermanentMember: user.isPermanentMember || false,
                isDataSyncEnabled: user.isDataSyncEnabled !== false,
                memberCode: user.memberCode || null,
                settings: user.settings || {
                  enableBiometric: false,
                  autoSync: true,
                  enableNotification: true,
                  theme: 'system',
                  language: 'zh-CN'
                },
                createdAt: user.createdAt || new Date().toISOString(),
                updatedAt: user.updatedAt || new Date().toISOString()
              },
              expiresIn: 86400
            }
          })
        };
      } else {
        console.log('Login failed - invalid credentials');
        console.log('Expected hash for testuser:', validUsers.testuser?.passwordHash);
        console.log('Received hash:', body.password);
        return {
          statusCode: 400,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
          body: JSON.stringify({
            success: false,
            message: '用户名或密码错误'
          })
        };
      }
    }

    // 获取套餐接口
    if ((path === '/api/packages' || path === '/packages') && method === 'GET') {
      return {
        statusCode: 200,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*'
        },
        body: JSON.stringify({
          success: true,
          data: [
            {
              id: 'monthly',
              name: '月度会员',
              description: '享受一个月的会员特权，包含所有高级功能',
              price: 18.88,
              durationDays: 30,
              features: [
                '无限制章节生成',
                '高级AI模型访问',
                '多格式导出',
                '扩展功能使用',
                '优先客服支持'
              ],
              limits: {
                maxChaptersPerNovel: -1,
                maxKnowledgeDocuments: 100,
                canUseExtendedFeatures: true,
                maxNovelsPerDay: -1,
                maxWordsPerGeneration: -1,
                canExportToMultipleFormats: true,
                canUseAdvancedAI: true,
                maxCustomCharacterTypes: -1
              },
              isActive: true,
              sortOrder: 1,
              createdAt: '2024-01-01T00:00:00.000Z',
              updatedAt: '2024-01-01T00:00:00.000Z'
            },
            {
              id: 'permanent',
              name: '永久会员',
              description: '一次购买，终身享受所有会员特权',
              price: 188.88,
              durationDays: -1,
              features: [
                '永久无限制章节生成',
                '永久高级AI模型访问',
                '永久多格式导出',
                '永久扩展功能使用',
                '永久优先客服支持',
                '未来新功能免费使用'
              ],
              limits: {
                maxChaptersPerNovel: -1,
                maxKnowledgeDocuments: -1,
                canUseExtendedFeatures: true,
                maxNovelsPerDay: -1,
                maxWordsPerGeneration: -1,
                canExportToMultipleFormats: true,
                canUseAdvancedAI: true,
                maxCustomCharacterTypes: -1
              },
              isActive: true,
              sortOrder: 2,
              createdAt: '2024-01-01T00:00:00.000Z',
              updatedAt: '2024-01-01T00:00:00.000Z'
            }
          ]
        })
      };
    }

    // 发送验证码接口
    if ((path === '/api/auth/send-code' || path === '/auth/send-code') && method === 'POST') {
      const body = event.body ? JSON.parse(event.body) : {};
      const phoneNumber = body.phoneNumber;

      console.log('发送验证码请求:', phoneNumber);

      if (!phoneNumber) {
        return {
          statusCode: 400,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
          body: JSON.stringify({
            success: false,
            message: '手机号不能为空'
          })
        };
      }

      // 生成6位验证码
      const verificationCode = Math.floor(100000 + Math.random() * 900000).toString();
      console.log(`为手机号 ${phoneNumber} 生成验证码: ${verificationCode}`);

      // TODO: 在这里集成真实的短信服务
      // 目前是模拟实现，实际部署时需要替换为真实的短信发送逻辑

      try {
        // 模拟短信发送（开发阶段）
        const smsResult = await sendSMS(phoneNumber, verificationCode);

        if (smsResult.success) {
          return {
            statusCode: 200,
            headers: {
              'Content-Type': 'application/json',
              'Access-Control-Allow-Origin': '*'
            },
            body: JSON.stringify({
              success: true,
              message: '验证码发送成功',
              // 开发阶段返回验证码，生产环境应该删除这行
              debugCode: verificationCode
            })
          };
        } else {
          return {
            statusCode: 500,
            headers: {
              'Content-Type': 'application/json',
              'Access-Control-Allow-Origin': '*'
            },
            body: JSON.stringify({
              success: false,
              message: '验证码发送失败，请稍后重试'
            })
          };
        }
      } catch (error) {
        console.error('发送验证码失败:', error);
        return {
          statusCode: 500,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
          body: JSON.stringify({
            success: false,
            message: '验证码发送失败，请稍后重试'
          })
        };
      }
    }

    // 用户注册接口
    if ((path === '/api/auth/register' || path === '/auth/register') && method === 'POST') {
      const body = event.body ? JSON.parse(event.body) : {};
      const { username, password, phoneNumber, verificationCode, memberCode } = body;

      console.log('用户注册请求:', { username, phoneNumber, memberCode });

      // 验证必填字段
      if (!username || !password || !phoneNumber) {
        return {
          statusCode: 400,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
          body: JSON.stringify({
            success: false,
            message: '用户名、密码和手机号不能为空'
          })
        };
      }

      try {
        // 检查用户名和手机号是否已存在（使用CloudBase数据库）
        try {
          const existingUserByUsername = await db.collection('users').where({
            username: username
          }).get();

          if (existingUserByUsername.data.length > 0) {
            return {
              statusCode: 400,
              headers: {
                'Content-Type': 'application/json',
                'Access-Control-Allow-Origin': '*'
              },
              body: JSON.stringify({
                success: false,
                message: '用户名已存在'
              })
            };
          }

          const existingUserByPhone = await db.collection('users').where({
            phoneNumber: phoneNumber
          }).get();

          if (existingUserByPhone.data.length > 0) {
            return {
              statusCode: 400,
              headers: {
                'Content-Type': 'application/json',
                'Access-Control-Allow-Origin': '*'
              },
              body: JSON.stringify({
                success: false,
                message: '手机号已被注册'
              })
            };
          }
        } catch (dbError) {
          console.log('数据库查询失败，使用本地文件备用:', dbError);
          // 如果数据库查询失败，使用本地文件作为备用
          const database = loadDatabase();

          const existingUserByUsername = database.users.find(u => u.username === username);
          if (existingUserByUsername) {
            return {
              statusCode: 400,
              headers: {
                'Content-Type': 'application/json',
                'Access-Control-Allow-Origin': '*'
              },
              body: JSON.stringify({
                success: false,
                message: '用户名已存在'
              })
            };
          }

          const existingUserByPhone = database.users.find(u => u.phoneNumber === phoneNumber);
          if (existingUserByPhone) {
            return {
              statusCode: 400,
              headers: {
                'Content-Type': 'application/json',
                'Access-Control-Allow-Origin': '*'
              },
              body: JSON.stringify({
                success: false,
                message: '手机号已被注册'
              })
            };
          }
        }

        // 验证会员码（如果提供）
        let memberInfo = null;
        if (memberCode) {
          try {
            const memberCodeResult = await db.collection('memberData').where({
              code: memberCode,
              isUsed: false
            }).get();

            if (memberCodeResult.data.length === 0) {
              return {
                statusCode: 400,
                headers: {
                  'Content-Type': 'application/json',
                  'Access-Control-Allow-Origin': '*'
                },
                body: JSON.stringify({
                  success: false,
                  message: '会员码无效或已被使用'
                })
              };
            }
            memberInfo = memberCodeResult.data[0];
          } catch (dbError) {
            console.log('会员码查询失败，使用本地文件备用:', dbError);
            // 如果数据库查询失败，使用本地文件作为备用
            const database = loadDatabase();
            const memberCodeData = database.memberCodes || [];
            memberInfo = memberCodeData.find(mc => mc.code === memberCode && !mc.isUsed);

            if (!memberInfo) {
              return {
                statusCode: 400,
                headers: {
                  'Content-Type': 'application/json',
                  'Access-Control-Allow-Origin': '*'
                },
                body: JSON.stringify({
                  success: false,
                  message: '会员码无效或已被使用'
                })
              };
            }
          }
        }

        // 创建新用户
        const userId = `user_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        const newUser = {
          id: userId,
          username,
          phoneNumber,
          email: null,
          avatar: null,
          passwordHash: password, // Flutter端已经哈希过了
          isMember: !!memberInfo,
          memberExpireTime: memberInfo ? null : null,
          membershipType: memberInfo ? 'permanent' : 'none',
          isPermanentMember: !!memberInfo,
          memberCode: memberCode || null,
          isDataSyncEnabled: true,
          settings: {
            enableBiometric: false,
            autoSync: true,
            enableNotification: true,
            theme: 'system',
            language: 'zh-CN'
          },
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        };

        // 保存用户到CloudBase数据库
        try {
          await db.collection('users').add(newUser);
          console.log('=== 用户成功保存到CloudBase数据库 ===');
          console.log('用户ID:', userId);
          console.log('用户名:', username);
          console.log('手机号:', phoneNumber);
          console.log('会员状态:', !!memberInfo);

          // 如果使用了会员码，标记为已使用
          if (memberInfo) {
            await db.collection('memberData').doc(memberInfo._id).update({
              isUsed: true,
              usedBy: userId,
              usedAt: new Date().toISOString()
            });
            console.log('会员码已标记为使用:', memberCode);
          }

        } catch (dbError) {
          console.error('保存用户到数据库失败:', dbError);
          console.log('=== 用户注册信息（数据库保存失败，仅记录日志） ===');
          console.log('用户ID:', userId);
          console.log('用户名:', username);
          console.log('手机号:', phoneNumber);
          console.log('会员状态:', !!memberInfo);
          console.log('会员码:', memberCode || '无');
          console.log('注册时间:', new Date().toISOString());
          console.log('用户数据:', JSON.stringify(newUser, null, 2));

          // 即使数据库保存失败，也继续返回成功响应（用户体验优先）
        }

        // 生成Token
        const token = `token-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
        const refreshToken = `refresh-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

        return {
          statusCode: 200,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
          body: JSON.stringify({
            success: true,
            data: {
              token,
              refreshToken,
              user: {
                id: newUser.id,
                username: newUser.username,
                phoneNumber: newUser.phoneNumber,
                email: newUser.email,
                avatar: newUser.avatar,
                isMember: newUser.isMember,
                memberExpireTime: newUser.memberExpireTime,
                membershipType: _normalizeMembershipType(newUser.membershipType),
                isPermanentMember: newUser.isPermanentMember,
                isDataSyncEnabled: newUser.isDataSyncEnabled,
                memberCode: newUser.memberCode,
                settings: newUser.settings,
                createdAt: newUser.createdAt,
                updatedAt: newUser.updatedAt
              },
              expiresIn: 86400
            }
          })
        };

      } catch (error) {
        console.error('用户注册失败:', error);
        return {
          statusCode: 500,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
          body: JSON.stringify({
            success: false,
            message: '注册失败，请稍后重试'
          })
        };
      }
    }

    // 验证验证码接口
    if ((path === '/api/auth/verify-code' || path === '/auth/verify-code') && method === 'POST') {
      const body = event.body ? JSON.parse(event.body) : {};
      if (body.code === '123456') {
        return {
          statusCode: 200,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
          body: JSON.stringify({
            success: true,
            message: '验证码验证成功'
          })
        };
      } else {
        return {
          statusCode: 400,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
          body: JSON.stringify({
            success: false,
            message: '验证码错误'
          })
        };
      }
    }

    // 验证会员码接口
    if ((path === '/api/member-code/validate' || path === '/member-code/validate') && method === 'POST') {
      const body = event.body ? JSON.parse(event.body) : {};
      if (body.code === 'VIP2024001') {
        return {
          statusCode: 200,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
          body: JSON.stringify({
            success: true,
            message: '会员码有效'
          })
        };
      } else {
        return {
          statusCode: 400,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*'
          },
          body: JSON.stringify({
            success: false,
            message: '会员码无效或已过期'
          })
        };
      }
    }

    // 数据同步下载接口
    if ((path === '/api/sync/download' || path === '/sync/download') && method === 'GET') {
      return {
        statusCode: 200,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*'
        },
        body: JSON.stringify({
          success: true,
          data: {
            novels: [],
            characterCards: [],
            characterTypes: [],
            knowledgeDocuments: [],
            stylePackages: [],
            userSettings: {
              enableBiometric: false,
              autoSync: true,
              enableNotification: true,
              theme: 'system',
              language: 'zh-CN'
            }
          }
        })
      };
    }

    // 数据同步上传接口
    if ((path === '/api/sync/upload' || path === '/sync/upload') && method === 'POST') {
      return {
        statusCode: 200,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*'
        },
        body: JSON.stringify({
          success: true,
          message: '数据同步成功'
        })
      };
    }

    // 默认404响应
    return {
      statusCode: 404,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*'
      },
      body: JSON.stringify({
        success: false,
        message: 'API endpoint not found',
        path: path,
        method: method
      })
    };

  } catch (error) {
    console.error('Function error:', error);
    return {
      statusCode: 500,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*'
      },
      body: JSON.stringify({
        success: false,
        error: error.message
      })
    };
  }
};
